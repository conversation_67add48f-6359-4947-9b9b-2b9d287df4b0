// Mocks generated by Mockito 5.4.6 from annotations
// in nextsportz_v2/test/features/auth/presentation/screens/register_success_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i3;

import 'package:fpdart/fpdart.dart' as _i4;
import 'package:mockito/mockito.dart' as _i1;
import 'package:mockito/src/dummies.dart' as _i7;
import 'package:nextsportz_v2/core/networking/app_error.dart' as _i5;
import 'package:nextsportz_v2/features/auth/domain/entities/user.dart' as _i6;
import 'package:nextsportz_v2/features/auth/domain/repositories/auth_repository.dart'
    as _i2;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

/// A class which mocks [AuthRepository].
///
/// See the documentation for Mockito's code generation for more information.
class MockAuthRepository extends _i1.Mock implements _i2.AuthRepository {
  MockAuthRepository() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i3.Future<_i4.Either<_i5.AppError, _i6.User>> loginWithPhone({
    required String? phoneNumber,
    required String? password,
    required String? role,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #loginWithPhone,
          [],
          {
            #phoneNumber: phoneNumber,
            #password: password,
            #role: role,
          },
        ),
        returnValue: _i3.Future<_i4.Either<_i5.AppError, _i6.User>>.value(
            _i7.dummyValue<_i4.Either<_i5.AppError, _i6.User>>(
          this,
          Invocation.method(
            #loginWithPhone,
            [],
            {
              #phoneNumber: phoneNumber,
              #password: password,
              #role: role,
            },
          ),
        )),
      ) as _i3.Future<_i4.Either<_i5.AppError, _i6.User>>);

  @override
  _i3.Future<_i4.Either<_i5.AppError, void>> register({
    required String? name,
    required String? email,
    required String? phoneNumber,
    required String? password,
    required String? role,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #register,
          [],
          {
            #name: name,
            #email: email,
            #phoneNumber: phoneNumber,
            #password: password,
            #role: role,
          },
        ),
        returnValue: _i3.Future<_i4.Either<_i5.AppError, void>>.value(
            _i7.dummyValue<_i4.Either<_i5.AppError, void>>(
          this,
          Invocation.method(
            #register,
            [],
            {
              #name: name,
              #email: email,
              #phoneNumber: phoneNumber,
              #password: password,
              #role: role,
            },
          ),
        )),
      ) as _i3.Future<_i4.Either<_i5.AppError, void>>);

  @override
  _i3.Future<_i4.Either<_i5.AppError, void>> verifyOtp({
    required String? phoneNumber,
    required String? otp,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #verifyOtp,
          [],
          {
            #phoneNumber: phoneNumber,
            #otp: otp,
          },
        ),
        returnValue: _i3.Future<_i4.Either<_i5.AppError, void>>.value(
            _i7.dummyValue<_i4.Either<_i5.AppError, void>>(
          this,
          Invocation.method(
            #verifyOtp,
            [],
            {
              #phoneNumber: phoneNumber,
              #otp: otp,
            },
          ),
        )),
      ) as _i3.Future<_i4.Either<_i5.AppError, void>>);

  @override
  _i3.Future<_i4.Either<_i5.AppError, void>> forgotPassword(
          {required String? email}) =>
      (super.noSuchMethod(
        Invocation.method(
          #forgotPassword,
          [],
          {#email: email},
        ),
        returnValue: _i3.Future<_i4.Either<_i5.AppError, void>>.value(
            _i7.dummyValue<_i4.Either<_i5.AppError, void>>(
          this,
          Invocation.method(
            #forgotPassword,
            [],
            {#email: email},
          ),
        )),
      ) as _i3.Future<_i4.Either<_i5.AppError, void>>);

  @override
  _i3.Future<_i4.Either<_i5.AppError, void>> resetPassword({
    required String? token,
    required String? newPassword,
    required String? confirmPassword,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #resetPassword,
          [],
          {
            #token: token,
            #newPassword: newPassword,
            #confirmPassword: confirmPassword,
          },
        ),
        returnValue: _i3.Future<_i4.Either<_i5.AppError, void>>.value(
            _i7.dummyValue<_i4.Either<_i5.AppError, void>>(
          this,
          Invocation.method(
            #resetPassword,
            [],
            {
              #token: token,
              #newPassword: newPassword,
              #confirmPassword: confirmPassword,
            },
          ),
        )),
      ) as _i3.Future<_i4.Either<_i5.AppError, void>>);

  @override
  _i3.Future<_i4.Either<_i5.AppError, void>> logout() => (super.noSuchMethod(
        Invocation.method(
          #logout,
          [],
        ),
        returnValue: _i3.Future<_i4.Either<_i5.AppError, void>>.value(
            _i7.dummyValue<_i4.Either<_i5.AppError, void>>(
          this,
          Invocation.method(
            #logout,
            [],
          ),
        )),
      ) as _i3.Future<_i4.Either<_i5.AppError, void>>);

  @override
  _i3.Future<_i4.Either<_i5.AppError, _i6.User?>> getCurrentUser() =>
      (super.noSuchMethod(
        Invocation.method(
          #getCurrentUser,
          [],
        ),
        returnValue: _i3.Future<_i4.Either<_i5.AppError, _i6.User?>>.value(
            _i7.dummyValue<_i4.Either<_i5.AppError, _i6.User?>>(
          this,
          Invocation.method(
            #getCurrentUser,
            [],
          ),
        )),
      ) as _i3.Future<_i4.Either<_i5.AppError, _i6.User?>>);

  @override
  _i3.Future<_i4.Either<_i5.AppError, void>> updateProfile({
    required String? name,
    required String? email,
    String? profileImage,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #updateProfile,
          [],
          {
            #name: name,
            #email: email,
            #profileImage: profileImage,
          },
        ),
        returnValue: _i3.Future<_i4.Either<_i5.AppError, void>>.value(
            _i7.dummyValue<_i4.Either<_i5.AppError, void>>(
          this,
          Invocation.method(
            #updateProfile,
            [],
            {
              #name: name,
              #email: email,
              #profileImage: profileImage,
            },
          ),
        )),
      ) as _i3.Future<_i4.Either<_i5.AppError, void>>);

  @override
  _i3.Future<_i4.Either<_i5.AppError, bool>> isAuthenticated() =>
      (super.noSuchMethod(
        Invocation.method(
          #isAuthenticated,
          [],
        ),
        returnValue: _i3.Future<_i4.Either<_i5.AppError, bool>>.value(
            _i7.dummyValue<_i4.Either<_i5.AppError, bool>>(
          this,
          Invocation.method(
            #isAuthenticated,
            [],
          ),
        )),
      ) as _i3.Future<_i4.Either<_i5.AppError, bool>>);
}
