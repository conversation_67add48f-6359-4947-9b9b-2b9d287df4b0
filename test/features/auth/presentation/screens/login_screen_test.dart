import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:nextsportz_v2/features/auth/presentation/screens/login_screen.dart';
import 'package:nextsportz_v2/features/auth/presentation/logic/controller.dart';
import 'package:nextsportz_v2/features/auth/presentation/logic/auth_state.dart';
import 'package:nextsportz_v2/features/auth/domain/entities/user.dart';
import 'package:nextsportz_v2/features/auth/domain/repositories/auth_repository.dart';
import 'package:nextsportz_v2/features/auth/data/repositories/auth_repository_provider.dart';

import 'login_screen_test.mocks.dart';

@GenerateMocks([AuthRepository])
void main() {
  group('LoginScreen', () {
    late MockAuthRepository mockAuthRepository;

    setUp(() {
      mockAuthRepository = MockAuthRepository();
    });

    Widget createTestWidget() {
      return ProviderScope(
        overrides: [
          authRepositoryProvider.overrideWithValue(mockAuthRepository),
        ],
        child: MaterialApp(home: const LoginScreen()),
      );
    }

    testWidgets('should render login screen with all elements', (
      WidgetTester tester,
    ) async {
      // Act
      await tester.pumpWidget(createTestWidget());

      // Assert
      expect(find.byType(LoginScreen), findsOneWidget);
      expect(
        find.byType(TextFormField),
        findsAtLeast(2),
      ); // Phone and password fields
      expect(find.byType(ElevatedButton), findsAtLeast(1)); // Login button
    });

    testWidgets('should show loading state when logging in', (
      WidgetTester tester,
    ) async {
      // Arrange
      when(mockAuthNotifier.state).thenReturn(AuthState.loading());

      // Act
      await tester.pumpWidget(createTestWidget());

      // Assert
      expect(find.byType(CircularProgressIndicator), findsAtLeast(1));
    });

    testWidgets('should show error state when login fails', (
      WidgetTester tester,
    ) async {
      // Arrange
      const errorMessage = 'Invalid credentials';
      when(mockAuthNotifier.state).thenReturn(AuthState.error(errorMessage));

      // Act
      await tester.pumpWidget(createTestWidget());

      // Assert
      expect(find.text(errorMessage), findsOneWidget);
    });

    testWidgets('should call login when form is submitted with valid data', (
      WidgetTester tester,
    ) async {
      // Arrange
      when(mockAuthNotifier.state).thenReturn(AuthState.unauthenticated());
      when(
        mockAuthNotifier.loginWithPhone(
          phoneNumber: '1234567890',
          password: 'password123',
          role: 'PLAYER',
        ),
      ).thenAnswer((_) async => true);

      // Act
      await tester.pumpWidget(createTestWidget());

      // Enter phone number
      await tester.enterText(find.byType(TextFormField).first, '1234567890');

      // Enter password
      await tester.enterText(find.byType(TextFormField).last, 'password123');

      // Tap login button
      await tester.tap(find.text('Log In'));
      await tester.pump();

      // Assert
      verify(
        mockAuthNotifier.loginWithPhone(
          phoneNumber: '1234567890',
          password: 'password123',
          role: 'PLAYER',
        ),
      ).called(1);
    });

    testWidgets('should navigate to forgot password screen', (
      WidgetTester tester,
    ) async {
      // Arrange
      when(mockAuthNotifier.state).thenReturn(AuthState.unauthenticated());

      // Act
      await tester.pumpWidget(createTestWidget());
      await tester.tap(find.text('Forgot Password?'));
      await tester.pumpAndSettle();

      // Assert
      // Should navigate to forgot password screen
      expect(find.byType(LoginScreen), findsNothing);
    });

    testWidgets('should navigate to register screen', (
      WidgetTester tester,
    ) async {
      // Arrange
      when(mockAuthNotifier.state).thenReturn(AuthState.unauthenticated());

      // Act
      await tester.pumpWidget(createTestWidget());
      await tester.tap(find.text('Don\'t have an account? Sign up'));
      await tester.pumpAndSettle();

      // Assert
      // Should navigate to register screen
      expect(find.byType(LoginScreen), findsNothing);
    });

    testWidgets('should show form validation errors', (
      WidgetTester tester,
    ) async {
      // Arrange
      when(mockAuthNotifier.state).thenReturn(AuthState.unauthenticated());

      // Act
      await tester.pumpWidget(createTestWidget());

      // Try to submit without entering data
      await tester.tap(find.text('Log In'));
      await tester.pump();

      // Assert
      // Should show validation errors
      expect(find.text('Please enter your phone number'), findsOneWidget);
      expect(find.text('Please enter your password'), findsOneWidget);
    });

    testWidgets('should handle phone number validation', (
      WidgetTester tester,
    ) async {
      // Arrange
      when(mockAuthNotifier.state).thenReturn(AuthState.unauthenticated());

      // Act
      await tester.pumpWidget(createTestWidget());

      // Enter invalid phone number
      await tester.enterText(find.byType(TextFormField).first, '123');

      await tester.tap(find.text('Log In'));
      await tester.pump();

      // Assert
      expect(find.text('Please enter a valid phone number'), findsOneWidget);
    });

    testWidgets('should handle password validation', (
      WidgetTester tester,
    ) async {
      // Arrange
      when(mockAuthNotifier.state).thenReturn(AuthState.unauthenticated());

      // Act
      await tester.pumpWidget(createTestWidget());

      // Enter short password
      await tester.enterText(find.byType(TextFormField).last, '123');

      await tester.tap(find.text('Log In'));
      await tester.pump();

      // Assert
      expect(
        find.text('Password must be at least 6 characters'),
        findsOneWidget,
      );
    });

    testWidgets('should toggle password visibility', (
      WidgetTester tester,
    ) async {
      // Arrange
      when(mockAuthNotifier.state).thenReturn(AuthState.unauthenticated());

      // Act
      await tester.pumpWidget(createTestWidget());

      // Find password field and toggle visibility
      final passwordField = find.byType(TextFormField).last;
      await tester.tap(find.byIcon(Icons.remove_red_eye_outlined));
      await tester.pump();

      // Assert
      expect(find.byIcon(Icons.remove_red_eye), findsOneWidget);
    });

    testWidgets('should show authenticated state and navigate to home', (
      WidgetTester tester,
    ) async {
      // Arrange
      final user = User(
        id: '1',
        name: 'Test User',
        email: '<EMAIL>',
        phoneNumber: '1234567890',
        role: 'PLAYER',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        isActive: true,
      );
      when(mockAuthNotifier.state).thenReturn(AuthState.authenticated(user));

      // Act
      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      // Assert
      // Should navigate to home screen when authenticated
      expect(find.byType(LoginScreen), findsNothing);
    });

    testWidgets('should clear error when tapping on error message', (
      WidgetTester tester,
    ) async {
      // Arrange
      const errorMessage = 'Invalid credentials';
      when(mockAuthNotifier.state).thenReturn(AuthState.error(errorMessage));

      // Act
      await tester.pumpWidget(createTestWidget());
      await tester.tap(find.text(errorMessage));
      await tester.pump();

      // Assert
      verify(mockAuthNotifier.clearError()).called(1);
    });

    testWidgets('should handle keyboard input correctly', (
      WidgetTester tester,
    ) async {
      // Arrange
      when(mockAuthNotifier.state).thenReturn(AuthState.unauthenticated());

      // Act
      await tester.pumpWidget(createTestWidget());

      // Test phone number input
      await tester.enterText(find.byType(TextFormField).first, '1234567890');
      await tester.pump();

      // Test password input
      await tester.enterText(find.byType(TextFormField).last, 'password123');
      await tester.pump();

      // Assert
      expect(find.text('1234567890'), findsOneWidget);
      expect(find.text('password123'), findsOneWidget);
    });

}
